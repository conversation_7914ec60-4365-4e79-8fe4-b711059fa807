using System.Collections.Generic;

namespace KMPS.Models
{
    public class Employee
    {
        public int Id { get; set; }
        public required string Name { get; set; }
        public int DepartmentId { get; set; }
        public required string Position { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Address { get; set; }
        public DateTime? JoinDate { get; set; }
        public string? Status { get; set; } = "نشط"; // Active
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? ModifiedAt { get; set; }

        public virtual Department Department { get; set; } = null!;
        public virtual ICollection<Project> ManagedProjects { get; set; } = [];
        public virtual ICollection<Task> AssignedTasks { get; set; } = [];
    }
}
