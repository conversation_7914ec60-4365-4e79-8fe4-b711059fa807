using System.Threading.Tasks;
using KMPS.Models;
using KMPS.Services;
using System.Windows.Input;

namespace KMPS.ViewModels.Dialogs
{
    public class EmployeeDialogViewModel : DialogViewModelBase
    {
        private Employee? _employee;
        private string _name = string.Empty;
        private string _position = string.Empty;
        private string _phoneNumber = string.Empty;
        private string _email = string.Empty;
        private Department? _department;
        
        public Employee Employee
        {
            get => _employee;
            set => SetProperty(ref _employee, value);
        }

        public string Name
        {
            get => _name;
            set
            {
                if (SetProperty(ref _name, value))
                {
                    ValidateInput();
                }
            }
        }

        public string Position
        {
            get => _position;
            set
            {
                if (SetProperty(ref _position, value))
                {
                    ValidateInput();
                }
            }
        }

        public string PhoneNumber
        {
            get => _phoneNumber;
            set
            {
                if (SetProperty(ref _phoneNumber, value))
                {
                    ValidateInput();
                }
            }
        }

        public string Email
        {
            get => _email;
            set
            {
                if (SetProperty(ref _email, value))
                {
                    ValidateInput();
                }
            }
        }

        public Department Department
        {
            get => _department;
            set => SetProperty(ref _department, value);
        }

        public EmployeeDialogViewModel(IDataService dataService, IDialogService dialogService, Employee? employee = null) 
            : base(dataService, dialogService, employee == null ? "إضافة موظف جديد" : "تعديل موظف")
        {
            if (employee != null)
            {
                Employee = employee;
                Name = employee.Name;
                Position = employee.Position;
                PhoneNumber = employee.PhoneNumber;
                Email = employee.Email;
                Department = employee.Department;
            }
            else
            {
                Employee = new Employee
                {
                    Name = "موظف جديد",
                    Position = "موظف"
                };
            }
        }

        public override async Task<bool> SaveAsync()
        {
            try
            {
                Employee.Name = Name;
                Employee.Position = Position;
                Employee.PhoneNumber = PhoneNumber;
                Employee.Email = Email;
                Employee.Department = Department;

                if (Employee.Id == 0)
                {
                    await DataService.AddEmployeeAsync(Employee);
                }
                else
                {
                    await DataService.UpdateEmployeeAsync(Employee);
                }
                return true;
            }
            catch (Exception ex)
            {
                await DialogService.ShowErrorAsync("خطأ", $"حدث خطأ أثناء حفظ الموظف: {ex.Message}");
                return false;
            }
        }

        protected override void ValidateInput()
        {
            IsValid = !string.IsNullOrWhiteSpace(Name) 
                  && !string.IsNullOrWhiteSpace(Position)
                  && Department != null;
        }
    }
}
