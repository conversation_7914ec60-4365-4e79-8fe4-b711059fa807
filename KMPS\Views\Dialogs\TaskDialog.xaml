<mah:MetroWindow x:Class="KMPS.Views.Dialogs.TaskDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:KMPS.Views.Dialogs"
             xmlns:vm="clr-namespace:KMPS.ViewModels.Dialogs"
             mc:Ignorable="d"
             Title="{Binding Title}"
             Height="550" Width="650"
             WindowStartupLocation="CenterOwner"
             FlowDirection="RightToLeft">

    <mah:MetroWindow.DataContext>
        <vm:TaskDialogViewModel />
    </mah:MetroWindow.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0"
                   Text="{Binding Title}"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   Margin="0,0,0,20"/>

        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,10">
                <TextBox
                    materialDesign:HintAssist.Hint="عنوان المهمة"
                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                    Text="{Binding Title, UpdateSourceTrigger=PropertyChanged}"
                    Margin="0,5"/>

                <TextBox
                    materialDesign:HintAssist.Hint="وصف المهمة"
                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                    Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                    TextWrapping="Wrap"
                    AcceptsReturn="True"
                    Height="100"
                    Margin="0,5"/>

                <ComboBox
                    materialDesign:HintAssist.Hint="المشروع"
                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                    ItemsSource="{Binding Projects}"
                    DisplayMemberPath="Name"
                    SelectedItem="{Binding Project}"
                    Margin="0,5"/>

                <DatePicker
                    materialDesign:HintAssist.Hint="تاريخ الاستحقاق"
                    Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                    SelectedDate="{Binding DueDate}"
                    Margin="0,5"/>

                <ComboBox
                    materialDesign:HintAssist.Hint="الأولوية"
                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                    SelectedItem="{Binding Priority}"
                    Margin="0,5">
                    <ComboBoxItem Content="منخفضة"/>
                    <ComboBoxItem Content="متوسطة"/>
                    <ComboBoxItem Content="عالية"/>
                    <ComboBoxItem Content="عاجلة"/>
                </ComboBox>

                <ComboBox
                    materialDesign:HintAssist.Hint="الحالة"
                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                    SelectedItem="{Binding Status}"
                    Margin="0,5">
                    <ComboBoxItem Content="جديدة"/>
                    <ComboBoxItem Content="قيد التنفيذ"/>
                    <ComboBoxItem Content="منتظرة"/>
                    <ComboBoxItem Content="مكتملة"/>
                    <ComboBoxItem Content="ملغاة"/>
                </ComboBox>

                <ComboBox
                    materialDesign:HintAssist.Hint="مسندة إلى"
                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                    ItemsSource="{Binding Employees}"
                    DisplayMemberPath="Name"
                    SelectedItem="{Binding AssignedTo}"
                    Margin="0,5"/>
            </StackPanel>
        </ScrollViewer>

        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Left"
                    Margin="0,20,0,0">
            <Button Content="حفظ"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                    IsEnabled="{Binding IsValid}"
                    Margin="0,0,10,0"/>
            <Button Content="إلغاء"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                    CommandParameter="False"/>
        </StackPanel>
    </Grid>
</mah:MetroWindow>
