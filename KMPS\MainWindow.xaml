<controls:MetroWindow x:Class="KMPS.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:controls="http://metro.mahapps.com/winfx/xaml/controls"
        xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:KMPS"
        mc:Ignorable="d"
        Title="نظام إدارة مشاريع بلدية كفرنجة" 
        Height="800" 
        Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="Arial"
        GlowBrush="{DynamicResource MahApps.Brushes.Accent}"
        WindowTitleBrush="{DynamicResource MahApps.Brushes.Accent}">
    <controls:MetroWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Light.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.BlueGrey.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Accent/MaterialDesignColor.Lime.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </controls:MetroWindow.Resources>

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Right Sidebar Navigation -->
        <materialDesign:Card Grid.Column="0"
                            Padding="8"
                            Margin="8">
            <StackPanel>
                <Button x:Name="DashboardButton"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        Margin="4"
                        Click="DashboardButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ViewDashboard" 
                                                Width="24" 
                                                Height="24" 
                                                Margin="0 0 8 0"/>
                        <TextBlock Text="لوحة التحكم"/>
                    </StackPanel>
                </Button>

                <Button x:Name="ProjectsButton"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        Margin="4"
                        Click="ProjectsButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FolderMultiple" 
                                                Width="24" 
                                                Height="24" 
                                                Margin="0 0 8 0"/>
                        <TextBlock Text="المشاريع"/>
                    </StackPanel>
                </Button>

                <Button x:Name="TasksButton"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        Margin="4"
                        Click="TasksButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="CheckboxMarkedOutline" 
                                                Width="24" 
                                                Height="24" 
                                                Margin="0 0 8 0"/>
                        <TextBlock Text="المهام"/>
                    </StackPanel>
                </Button>

                <Button x:Name="EmployeesButton"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        Margin="4"
                        Click="EmployeesButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AccountMultiple" 
                                                Width="24" 
                                                Height="24" 
                                                Margin="0 0 8 0"/>
                        <TextBlock Text="الموظفون"/>
                    </StackPanel>
                </Button>

                <Button x:Name="ReportsButton"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        Margin="4"
                        Click="ReportsButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FileDocumentMultiple" 
                                                Width="24" 
                                                Height="24" 
                                                Margin="0 0 8 0"/>
                        <TextBlock Text="التقارير"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </materialDesign:Card>

        <!-- Main Content Area -->
        <materialDesign:Card Grid.Column="1"
                            Margin="8">
            <ContentControl x:Name="MainContent"/>
        </materialDesign:Card>
    </Grid>
    </materialDesign:DialogHost>
</controls:MetroWindow>
