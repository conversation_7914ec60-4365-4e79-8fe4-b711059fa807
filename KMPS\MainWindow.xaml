<controls:MetroWindow x:Class="KMPS.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:controls="http://metro.mahapps.com/winfx/xaml/controls"
        xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:KMPS"
        mc:Ignorable="d"
        Title="نظام إدارة مشاريع بلدية كفرنجة - KMPS"
        Height="900"
        Width="1400"
        MinHeight="700"
        MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="14"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="#F5F5F5"
        FontFamily="Segoe UI"
        GlowBrush="#2196F3"
        WindowTitleBrush="#1976D2"
        TitleCharacterCasing="Normal">
    <controls:MetroWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Light.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Blue.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Accent/MaterialDesignColor.LightBlue.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Custom Styles -->
            <Style x:Key="ModernNavButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
                <Setter Property="Height" Value="60"/>
                <Setter Property="Margin" Value="8,4"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#E3F2FD"/>
                        <Setter Property="Foreground" Value="#1976D2"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="SidebarCard" TargetType="materialDesign:Card">
                <Setter Property="Background" Value="White"/>
                <Setter Property="Margin" Value="16,16,8,16"/>
                <Setter Property="Padding" Value="0"/>
            </Style>

            <Style x:Key="MainContentCard" TargetType="materialDesign:Card">
                <Setter Property="Background" Value="White"/>
                <Setter Property="Margin" Value="8,16,16,16"/>
                <Setter Property="Padding" Value="24"/>
            </Style>
        </ResourceDictionary>
    </controls:MetroWindow.Resources>

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="280"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Modern Sidebar Navigation -->
            <materialDesign:Card Grid.Column="0" Style="{StaticResource SidebarCard}">
                <StackPanel>
                    <!-- Header Section -->
                    <Border Background="#1976D2" CornerRadius="12,12,0,0" Padding="20,24">
                        <StackPanel>
                            <materialDesign:PackIcon Kind="City"
                                                   Width="48"
                                                   Height="48"
                                                   Foreground="White"
                                                   HorizontalAlignment="Center"/>
                            <TextBlock Text="بلدية كفرنجة"
                                     FontSize="20"
                                     FontWeight="Bold"
                                     Foreground="White"
                                     HorizontalAlignment="Center"
                                     Margin="0,8,0,0"/>
                            <TextBlock Text="نظام إدارة المشاريع"
                                     FontSize="12"
                                     Foreground="#BBDEFB"
                                     HorizontalAlignment="Center"
                                     Margin="0,4,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Navigation Buttons -->
                    <StackPanel Margin="0,16,0,0">
                        <Button x:Name="DashboardButton"
                                Style="{StaticResource ModernNavButton}"
                                Click="DashboardButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="لوحة التحكم" VerticalAlignment="Center"/>
                                <materialDesign:PackIcon Kind="ViewDashboard"
                                                       Width="28"
                                                       Height="28"
                                                       Margin="12,0,0,0"
                                                       VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ProjectsButton"
                                Style="{StaticResource ModernNavButton}"
                                Click="ProjectsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="المشاريع" VerticalAlignment="Center"/>
                                <materialDesign:PackIcon Kind="FolderMultiple"
                                                       Width="28"
                                                       Height="28"
                                                       Margin="12,0,0,0"
                                                       VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="TasksButton"
                                Style="{StaticResource ModernNavButton}"
                                Click="TasksButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="المهام" VerticalAlignment="Center"/>
                                <materialDesign:PackIcon Kind="CheckboxMarkedOutline"
                                                       Width="28"
                                                       Height="28"
                                                       Margin="12,0,0,0"
                                                       VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="EmployeesButton"
                                Style="{StaticResource ModernNavButton}"
                                Click="EmployeesButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="الموظفون" VerticalAlignment="Center"/>
                                <materialDesign:PackIcon Kind="AccountMultiple"
                                                       Width="28"
                                                       Height="28"
                                                       Margin="12,0,0,0"
                                                       VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ReportsButton"
                                Style="{StaticResource ModernNavButton}"
                                Click="ReportsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="التقارير" VerticalAlignment="Center"/>
                                <materialDesign:PackIcon Kind="ChartLine"
                                                       Width="28"
                                                       Height="28"
                                                       Margin="12,0,0,0"
                                                       VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>

                    <!-- Footer Section -->
                    <Border Margin="0,32,0,0" Padding="20,16" Background="#F5F5F5">
                        <StackPanel>
                            <TextBlock Text="الإصدار 1.0"
                                     FontSize="11"
                                     Foreground="#757575"
                                     HorizontalAlignment="Center"/>
                            <TextBlock Text="© 2024 بلدية كفرنجة"
                                     FontSize="10"
                                     Foreground="#9E9E9E"
                                     HorizontalAlignment="Center"
                                     Margin="0,4,0,0"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </materialDesign:Card>

            <!-- Modern Main Content Area -->
            <materialDesign:Card Grid.Column="1" Style="{StaticResource MainContentCard}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Content Header -->
                    <Border Grid.Row="0"
                           Background="#FAFAFA"
                           Margin="-24,-24,-24,16"
                           Padding="24,16"
                           CornerRadius="12,12,0,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon x:Name="ContentIcon"
                                                   Kind="ViewDashboard"
                                                   Width="32"
                                                   Height="32"
                                                   Foreground="#1976D2"
                                                   VerticalAlignment="Center"/>
                            <TextBlock x:Name="ContentTitle"
                                     Text="لوحة التحكم"
                                     FontSize="24"
                                     FontWeight="Bold"
                                     Foreground="#1976D2"
                                     VerticalAlignment="Center"
                                     Margin="16,0,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Main Content -->
                    <ContentControl x:Name="MainContent" Grid.Row="1"/>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </materialDesign:DialogHost>
</controls:MetroWindow>
