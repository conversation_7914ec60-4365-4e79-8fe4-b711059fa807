using System.Collections.ObjectModel;
using KMPS.Models;
using KMPS.Services;

namespace KMPS.ViewModels
{
    public class DashboardViewModel : BaseViewModel
    {
        private int _totalProjects;
        private int _totalTasks;
        private int _totalEmployees;
        private int _completedTasks;
        private ObservableCollection<Project> _recentProjects = [];
        private ObservableCollection<Models.Task> _upcomingTasks = [];
        private bool _isLoading;
        private string _errorMessage = string.Empty;

        public int TotalProjects
        {
            get => _totalProjects;
            set => SetProperty(ref _totalProjects, value);
        }

        public int TotalTasks
        {
            get => _totalTasks;
            set => SetProperty(ref _totalTasks, value);
        }

        public int TotalEmployees
        {
            get => _totalEmployees;
            set => SetProperty(ref _totalEmployees, value);
        }

        public int CompletedTasks
        {
            get => _completedTasks;
            set => SetProperty(ref _completedTasks, value);
        }

        public ObservableCollection<Project> RecentProjects
        {
            get => _recentProjects;
            set => SetProperty(ref _recentProjects, value);
        }

        public ObservableCollection<Models.Task> UpcomingTasks
        {
            get => _upcomingTasks;
            set => SetProperty(ref _upcomingTasks, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        private readonly IDataService _dataService;
        private readonly IDialogService _dialogService;

        public DashboardViewModel(IDataService dataService, IDialogService dialogService)
        {
            _dataService = dataService;
            _dialogService = dialogService;
            _ = InitializeAsync();
        }

        private async System.Threading.Tasks.Task InitializeAsync()
        {
            await RefreshDashboardAsync();
        }

        private async System.Threading.Tasks.Task RefreshDashboardAsync()
        {
            try
            {
                IsLoading = true;
                ErrorMessage = string.Empty;

                var dashboardData = await _dataService.GetDashboardDataAsync();
                
                TotalProjects = dashboardData.TotalProjects;
                TotalTasks = dashboardData.TotalTasks;
                TotalEmployees = dashboardData.TotalEmployees;
                CompletedTasks = dashboardData.CompletedTasks;
                
                RecentProjects = new ObservableCollection<Project>(dashboardData.RecentProjects);
                UpcomingTasks = new ObservableCollection<Models.Task>(dashboardData.UpcomingTasks);
            }
            catch (Exception ex)
            {
                ErrorMessage = ex.Message;
                await _dialogService.ShowErrorAsync("خطأ", "حدث خطأ أثناء تحميل البيانات");
            }
            finally
            {
                IsLoading = false;
            }
        }
    }
}
