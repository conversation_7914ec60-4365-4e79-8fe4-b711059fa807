using System.Collections.Generic;
using KMPS.Models;

namespace KMPS.Services
{
    public interface IDataService
    {
        Task<IEnumerable<Department>> GetDepartmentsAsync();
        Task<IEnumerable<Employee>> GetEmployeesAsync();
        Task<IEnumerable<Project>> GetProjectsAsync();
        Task<IEnumerable<Models.Task>> GetTasksAsync();
        
        Task<Department> GetDepartmentByIdAsync(int id);
        Task<Employee> GetEmployeeByIdAsync(int id);
        Task<Project> GetProjectByIdAsync(int id);
        Task<Models.Task> GetTaskByIdAsync(int id);
        
        Task<Department> AddDepartmentAsync(Department department);
        Task<Employee> AddEmployeeAsync(Employee employee);
        Task<Project> AddProjectAsync(Project project);
        Task<Models.Task> AddTaskAsync(Models.Task task);
        
        Task<bool> UpdateDepartmentAsync(Department department);
        Task<bool> UpdateEmployeeAsync(Employee employee);
        Task<bool> UpdateProjectAsync(Project project);
        Task<bool> UpdateTaskAsync(Models.Task task);
        
        Task<bool> DeleteDepartmentAsync(int id);
        Task<bool> DeleteEmployeeAsync(int id);
        Task<bool> DeleteProjectAsync(int id);
        Task<bool> DeleteTaskAsync(int id);
        
        Task<Dashboard> GetDashboardDataAsync();
    }

    public class Dashboard
    {
        public int TotalProjects { get; set; }
        public int TotalTasks { get; set; }
        public int TotalEmployees { get; set; }
        public int CompletedTasks { get; set; }
        public required IEnumerable<Project> RecentProjects { get; set; }
        public required IEnumerable<Task> UpcomingTasks { get; set; }
    }
}
