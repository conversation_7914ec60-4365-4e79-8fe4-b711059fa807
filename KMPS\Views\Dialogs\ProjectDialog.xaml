<mah:MetroWindow x:Class="KMPS.Views.Dialogs.ProjectDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:KMPS.Views.Dialogs"
             xmlns:vm="clr-namespace:KMPS.ViewModels.Dialogs"
             mc:Ignorable="d"
             Title="{Binding Title}"
             Height="600" Width="700"
             WindowStartupLocation="CenterOwner"
             FlowDirection="RightToLeft">

    <mah:MetroWindow.DataContext>
        <vm:ProjectDialogViewModel />
    </mah:MetroWindow.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0"
                   Text="{Binding Title}"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   Margin="0,0,0,20"/>

        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,10">
                <TextBox
                    materialDesign:HintAssist.Hint="اسم المشروع"
                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                    Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                    Margin="0,5"/>

                <TextBox
                    materialDesign:HintAssist.Hint="وصف المشروع"
                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                    Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                    TextWrapping="Wrap"
                    AcceptsReturn="True"
                    Height="100"
                    Margin="0,5"/>

                <DatePicker
                    materialDesign:HintAssist.Hint="تاريخ البداية"
                    Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                    SelectedDate="{Binding StartDate}"
                    Margin="0,5"/>

                <DatePicker
                    materialDesign:HintAssist.Hint="تاريخ النهاية"
                    Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                    SelectedDate="{Binding EndDate}"
                    Margin="0,5"/>

                <TextBox
                    materialDesign:HintAssist.Hint="الميزانية"
                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                    Text="{Binding Budget, StringFormat=N2, UpdateSourceTrigger=PropertyChanged}"
                    Margin="0,5"/>

                <ComboBox
                    materialDesign:HintAssist.Hint="حالة المشروع"
                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                    SelectedItem="{Binding Status}"
                    Margin="0,5">
                    <ComboBoxItem Content="جديد"/>
                    <ComboBoxItem Content="قيد التنفيذ"/>
                    <ComboBoxItem Content="معلق"/>
                    <ComboBoxItem Content="مكتمل"/>
                    <ComboBoxItem Content="ملغى"/>
                </ComboBox>

                <ComboBox
                    materialDesign:HintAssist.Hint="مدير المشروع"
                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                    ItemsSource="{Binding Employees}"
                    DisplayMemberPath="Name"
                    SelectedItem="{Binding ProjectManager}"
                    Margin="0,5"/>
            </StackPanel>
        </ScrollViewer>

        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Left"
                    Margin="0,20,0,0">
            <Button Content="حفظ"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                    IsEnabled="{Binding IsValid}"
                    Margin="0,0,10,0"/>
            <Button Content="إلغاء"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                    CommandParameter="False"/>
        </StackPanel>
    </Grid>
</mah:MetroWindow>
