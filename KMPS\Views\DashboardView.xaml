<UserControl x:Class="KMPS.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d">
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <TextBlock Text="لوحة التحكم"
                   Grid.Row="0"
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   Margin="0 0 0 16"/>

        <WrapPanel Grid.Row="1" Orientation="Horizontal">
            <!-- Project Statistics Card -->
            <materialDesign:Card Margin="8" Width="300" Height="150" Padding="16">
                <StackPanel>
                    <DockPanel>
                        <materialDesign:PackIcon Kind="FolderMultiple" 
                                               Width="24" 
                                               Height="24"/>
                        <TextBlock Text="المشاريع" 
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="8 0 0 0"/>
                    </DockPanel>
                    <TextBlock Text="10" 
                             Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                             Margin="0 16 0 0"/>
                    <TextBlock Text="مشاريع نشطة" 
                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                             Opacity="0.6"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Tasks Statistics Card -->
            <materialDesign:Card Margin="8" Width="300" Height="150" Padding="16">
                <StackPanel>
                    <DockPanel>
                        <materialDesign:PackIcon Kind="CheckboxMarkedOutline" 
                                               Width="24" 
                                               Height="24"/>
                        <TextBlock Text="المهام" 
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="8 0 0 0"/>
                    </DockPanel>
                    <TextBlock Text="25" 
                             Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                             Margin="0 16 0 0"/>
                    <TextBlock Text="مهام قيد التنفيذ" 
                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                             Opacity="0.6"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Employees Statistics Card -->
            <materialDesign:Card Margin="8" Width="300" Height="150" Padding="16">
                <StackPanel>
                    <DockPanel>
                        <materialDesign:PackIcon Kind="AccountMultiple" 
                                               Width="24" 
                                               Height="24"/>
                        <TextBlock Text="الموظفون" 
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Margin="8 0 0 0"/>
                    </DockPanel>
                    <TextBlock Text="15" 
                             Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                             Margin="0 16 0 0"/>
                    <TextBlock Text="موظف نشط" 
                             Style="{StaticResource MaterialDesignBody1TextBlock}"
                             Opacity="0.6"/>
                </StackPanel>
            </materialDesign:Card>
        </WrapPanel>
    </Grid>
</UserControl>
