using System.Threading.Tasks;
using KMPS.Services;

namespace KMPS.ViewModels.Dialogs
{
    public abstract class DialogViewModelBase : BaseViewModel
    {
        public string Title { get; set; }
        public bool IsValid { get; protected set; }

        protected readonly IDataService DataService;
        protected readonly IDialogService DialogService;

        protected DialogViewModelBase(IDataService dataService, IDialogService dialogService, string title)
        {
            DataService = dataService;
            DialogService = dialogService;
            Title = title;
        }

        public virtual Task InitializeAsync()
        {
            return Task.CompletedTask;
        }

        public virtual Task<bool> SaveAsync()
        {
            return Task.FromResult(false);
        }

        protected virtual void ValidateInput()
        {
            IsValid = true;
        }
    }
}
