<UserControl x:Class="KMPS.Views.ReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d">
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <TextBlock Text="التقارير"
                   Grid.Row="0"
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   Margin="0 0 0 16"/>

        <materialDesign:Card Grid.Row="1" Padding="16">
            <StackPanel>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <ComboBox materialDesign:HintAssist.Hint="نوع التقرير"
                              Grid.Column="0"
                              Margin="8"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}">
                        <ComboBoxItem Content="تقرير المشاريع الحالية"/>
                        <ComboBoxItem Content="تقرير المهام المتأخرة"/>
                        <ComboBoxItem Content="تقرير إنجازات الموظفين"/>
                        <ComboBoxItem Content="تقرير الميزانية"/>
                    </ComboBox>

                    <ComboBox materialDesign:HintAssist.Hint="قالب التقرير"
                              Grid.Column="1"
                              Margin="8"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}">
                        <ComboBoxItem Content="قالب تفصيلي"/>
                        <ComboBoxItem Content="قالب ملخص"/>
                        <ComboBoxItem Content="قالب تنفيذي"/>
                    </ComboBox>

                    <Button Grid.Column="2"
                            Margin="8"
                            Style="{StaticResource MaterialDesignRaisedButton}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileDocumentOutline" 
                                                   Width="24" 
                                                   Height="24"/>
                            <TextBlock Text="إنشاء التقرير" 
                                     Margin="8 0 0 0"/>
                        </StackPanel>
                    </Button>
                </Grid>

                <materialDesign:Card Margin="0 16 0 0"
                                   UniformCornerRadius="4">
                    <TextBlock Text="منطقة عرض التقارير - سيتم تطويرها لاحقاً"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Height="600"/>
                </materialDesign:Card>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
