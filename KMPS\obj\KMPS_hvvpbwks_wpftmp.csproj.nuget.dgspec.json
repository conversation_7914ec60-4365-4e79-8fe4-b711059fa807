{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\KMPS\\KMPS\\KMPS.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\KMPS\\KMPS\\KMPS.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\KMPS\\KMPS\\KMPS.csproj", "projectName": "KMPS", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\KMPS\\KMPS\\KMPS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\KMPS\\KMPS\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"EntityFrameworkCore.Jet": {"target": "Package", "version": "[9.0.0, )"}, "MahApps.Metro": {"target": "Package", "version": "[2.4.10, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.2.1, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.ReportViewer.WinForms": {"target": "Package", "version": "[10.0.40219.1, )"}, "System.Data.OleDb": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}