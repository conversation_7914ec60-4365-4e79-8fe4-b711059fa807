using System.Threading.Tasks;
using MahApps.Metro.Controls;
using MahApps.Metro.Controls.Dialogs;
using KMPS.Models;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;

namespace KMPS.Services
{
    public class DialogService : IDialogService
    {
        private readonly MetroWindow? _mainWindow;

        public DialogService()
        {
            _mainWindow = Application.Current.MainWindow as MetroWindow;
        }

        public async Task<bool> ShowConfirmationAsync(string title, string message)
        {
            if (_mainWindow == null) return false;

            var result = await _mainWindow.ShowMessageAsync(title, message,
                MessageDialogStyle.AffirmativeAndNegative,
                new MetroDialogSettings
                {
                    AffirmativeButtonText = "نعم",
                    NegativeButtonText = "لا",
                    DialogResultOnCancel = MessageDialogResult.Negative,
                    AnimateShow = true,
                    AnimateHide = true
                });

            return result == MessageDialogResult.Affirmative;
        }

        public async System.Threading.Tasks.Task ShowErrorAsync(string title, string message)
        {
            if (_mainWindow == null) return;

            await _mainWindow.ShowMessageAsync(title, message,
                MessageDialogStyle.Affirmative,
                new MetroDialogSettings
                {
                    AffirmativeButtonText = "حسناً",
                    DialogResultOnCancel = MessageDialogResult.Affirmative,
                    AnimateShow = true,
                    AnimateHide = true,
                    ColorScheme = MetroDialogColorScheme.Accented
                });
        }

        public async System.Threading.Tasks.Task ShowInfoAsync(string title, string message)
        {
            if (_mainWindow == null) return;

            await _mainWindow.ShowMessageAsync(title, message,
                MessageDialogStyle.Affirmative,
                new MetroDialogSettings
                {
                    AffirmativeButtonText = "حسناً",
                    DialogResultOnCancel = MessageDialogResult.Affirmative,
                    AnimateShow = true,
                    AnimateHide = true,
                    ColorScheme = MetroDialogColorScheme.Accented
                });
        }

        public async Task<Department?> ShowDepartmentDialogAsync(Department? department = null)
        {
            var dataService = ((App)Application.Current).Services.GetRequiredService<IDataService>();
            var viewModel = new ViewModels.Dialogs.DepartmentDialogViewModel(dataService, this, department);
            
            var view = new Views.Dialogs.DepartmentDialog
            {
                DataContext = viewModel
            };

            var result = await MaterialDesignThemes.Wpf.DialogHost.Show(view, "RootDialog");
            
            if (result is bool boolResult && boolResult)
            {
                if (await viewModel.SaveAsync())
                {
                    return department;
                }
            }

            return null;
        }

        public async Task<Employee?> ShowEmployeeDialogAsync(Employee? employee = null)
        {
            // Will implement custom dialog windows for each entity
            await Task.Delay(100); // Placeholder
            return null;
        }

        public async Task<Project?> ShowProjectDialogAsync(Project? project = null)
        {
            // Will implement custom dialog windows for each entity
            await Task.Delay(100); // Placeholder
            return null;
        }

        public async Task<Models.Task?> ShowTaskDialogAsync(Models.Task? task = null)
        {
            // Will implement custom dialog windows for each entity
            await Task.Delay(100); // Placeholder
            return null;
        }
    }
}
