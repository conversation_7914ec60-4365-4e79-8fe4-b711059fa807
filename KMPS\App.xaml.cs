﻿using System;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using KMPS.Models;
using KMPS.Services;
using KMPS.ViewModels;
using Microsoft.EntityFrameworkCore;

namespace KMPS
{
    public partial class App : Application
    {
        private ServiceProvider _serviceProvider;
        public IServiceProvider Services => _serviceProvider;

        public App()
        {
            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();
        }

        private void ConfigureServices(IServiceCollection services)
        {
            // Database context
            services.AddDbContext<KMPSDbContext>(options =>
                options.UseJet("Provider=Microsoft.ACE.OLEDB.12.0;Data Source=KMPSDB.accdb;"));

            // Services
            services.AddSingleton<IDialogService, DialogService>();
            services.AddScoped<IDataService, DataService>();

            // ViewModels
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<ProjectsViewModel>();
            services.AddTransient<TasksViewModel>();
            services.AddTransient<EmployeesViewModel>();

            // Main window
            services.AddSingleton<MainWindow>();
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
            mainWindow.Show();
        }

        protected override void OnExit(ExitEventArgs e)
        {
            base.OnExit(e);
            
            if (_serviceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
    }
}

