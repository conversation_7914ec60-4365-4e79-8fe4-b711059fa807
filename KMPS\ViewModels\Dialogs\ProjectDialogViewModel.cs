using System;
using System.Threading.Tasks;
using KMPS.Models;
using KMPS.Services;
using System.Windows.Input;

namespace KMPS.ViewModels.Dialogs
{
    public class ProjectDialogViewModel : DialogViewModelBase
    {
        private Project _project;
        private string _name;
        private string _description;
        private DateTime _startDate;
        private DateTime _endDate;
        private decimal _budget;
        private string _status;
        private Employee _projectManager;
        
        public Project Project
        {
            get => _project;
            set => SetProperty(ref _project, value);
        }

        public string Name
        {
            get => _name;
            set
            {
                if (SetProperty(ref _name, value))
                {
                    ValidateInput();
                }
            }
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                if (SetProperty(ref _startDate, value))
                {
                    ValidateInput();
                }
            }
        }

        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                if (SetProperty(ref _endDate, value))
                {
                    ValidateInput();
                }
            }
        }

        public decimal Budget
        {
            get => _budget;
            set => SetProperty(ref _budget, value);
        }

        public string Status
        {
            get => _status;
            set
            {
                if (SetProperty(ref _status, value))
                {
                    ValidateInput();
                }
            }
        }

        public Employee ProjectManager
        {
            get => _projectManager;
            set
            {
                if (SetProperty(ref _projectManager, value))
                {
                    ValidateInput();
                }
            }
        }

        public ProjectDialogViewModel(IDataService dataService, IDialogService dialogService, Project? project = null) 
            : base(dataService, dialogService, project == null ? "إضافة مشروع جديد" : "تعديل مشروع")
        {
            if (project != null)
            {
                Project = project;
                Name = project.Name;
                Description = project.Description;
                StartDate = project.StartDate;
                EndDate = project.EndDate;
                Budget = project.Budget;
                Status = project.Status;
                ProjectManager = project.ProjectManager;
            }
            else
            {
                Project = new Project
                {
                    Name = "مشروع جديد",
                    Status = "جديد",
                    StartDate = DateTime.Today,
                    EndDate = DateTime.Today.AddMonths(1)
                };
                StartDate = Project.StartDate;
                EndDate = Project.EndDate;
            }
        }

        public override async Task<bool> SaveAsync()
        {
            try
            {
                Project.Name = Name;
                Project.Description = Description;
                Project.StartDate = StartDate;
                Project.EndDate = EndDate;
                Project.Budget = Budget;
                Project.Status = Status;
                Project.ProjectManager = ProjectManager;

                if (Project.Id == 0)
                {
                    await DataService.AddProjectAsync(Project);
                }
                else
                {
                    await DataService.UpdateProjectAsync(Project);
                }
                return true;
            }
            catch (Exception ex)
            {
                await DialogService.ShowErrorAsync("خطأ", $"حدث خطأ أثناء حفظ المشروع: {ex.Message}");
                return false;
            }
        }

        protected override void ValidateInput()
        {
            IsValid = !string.IsNullOrWhiteSpace(Name) 
                  && !string.IsNullOrWhiteSpace(Status)
                  && EndDate >= StartDate
                  && ProjectManager != null;
        }
    }
}
