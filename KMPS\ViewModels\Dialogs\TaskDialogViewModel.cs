using System.Threading.Tasks;
using KMPS.Models;
using KMPS.Services;
using System.Windows.Input;

namespace KMPS.ViewModels.Dialogs
{
    public class TaskDialogViewModel : DialogViewModelBase
    {
        private Models.Task? _task;
        private string _title = string.Empty;
        private string _description = string.Empty;
        private DateTime _dueDate;
        private string _priority = string.Empty;
        private string _status = string.Empty;
        private Employee? _assignedTo;
        private Project? _project;
        
        public Models.Task? Task
        {
            get => _task;
            set => SetProperty(ref _task, value);
        }

        public new string Title
        {
            get => _title;
            set
            {
                if (SetProperty(ref _title, value))
                {
                    ValidateInput();
                }
            }
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public DateTime DueDate
        {
            get => _dueDate;
            set
            {
                if (SetProperty(ref _dueDate, value))
                {
                    ValidateInput();
                }
            }
        }

        public string Priority
        {
            get => _priority;
            set
            {
                if (SetProperty(ref _priority, value))
                {
                    ValidateInput();
                }
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                if (SetProperty(ref _status, value))
                {
                    ValidateInput();
                }
            }
        }

        public Employee? AssignedTo
        {
            get => _assignedTo;
            set
            {
                if (SetProperty(ref _assignedTo, value))
                {
                    ValidateInput();
                }
            }
        }

        public Project? Project
        {
            get => _project;
            set
            {
                if (SetProperty(ref _project, value))
                {
                    ValidateInput();
                }
            }
        }

        public TaskDialogViewModel(IDataService dataService, IDialogService dialogService, Models.Task? task = null) 
            : base(dataService, dialogService, task == null ? "إضافة مهمة جديدة" : "تعديل مهمة")
        {
            if (task != null)
            {
                Task = task;
                Title = task.Title;
                Description = task.Description ?? string.Empty;
                DueDate = task.DueDate;
                Priority = task.Priority;
                Status = task.Status;
                AssignedTo = task.AssignedTo;
                Project = task.Project;
            }
            else
            {
                Task = new Models.Task
                {
                    Title = "مهمة جديدة",
                    Priority = "متوسطة",
                    DueDate = DateTime.Today.AddDays(7),
                    Status = "جديدة"
                };
                DueDate = Task.DueDate;
                Status = Task.Status;
            }
        }

        public override async Task<bool> SaveAsync()
        {
            try
            {
                if (Task == null) return false;

                Task.Title = Title;
                Task.Description = Description;
                Task.DueDate = DueDate;
                Task.Priority = Priority;
                Task.Status = Status;
                Task.AssignedTo = AssignedTo!;
                Task.Project = Project!;

                if (Task.Id == 0)
                {
                    await DataService.AddTaskAsync(Task);
                }
                else
                {
                    await DataService.UpdateTaskAsync(Task);
                }
                return true;
            }
            catch (Exception ex)
            {
                await DialogService.ShowErrorAsync("خطأ", $"حدث خطأ أثناء حفظ المهمة: {ex.Message}");
                return false;
            }
        }

        protected override void ValidateInput()
        {
            IsValid = !string.IsNullOrWhiteSpace(Title) 
                  && !string.IsNullOrWhiteSpace(Priority)
                  && !string.IsNullOrWhiteSpace(Status)
                  && AssignedTo != null
                  && Project != null;
        }
    }
}
