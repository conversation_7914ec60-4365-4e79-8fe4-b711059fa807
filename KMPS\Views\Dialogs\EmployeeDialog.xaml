<mah:MetroWindow x:Class="KMPS.Views.Dialogs.EmployeeDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:KMPS.Views.Dialogs"
             xmlns:vm="clr-namespace:KMPS.ViewModels.Dialogs"
             mc:Ignorable="d"
             Title="{Binding Title}"
             Height="450" Width="600"
             WindowStartupLocation="CenterOwner"
             FlowDirection="RightToLeft">

    <mah:MetroWindow.DataContext>
        <vm:EmployeeDialogViewModel />
    </mah:MetroWindow.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0"
                   Text="{Binding Title}"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   Margin="0,0,0,20"/>

        <StackPanel Grid.Row="1" Margin="0,10">
            <TextBox
                materialDesign:HintAssist.Hint="اسم الموظف"
                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                Margin="0,5"/>

            <TextBox
                materialDesign:HintAssist.Hint="المنصب الوظيفي"
                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                Text="{Binding Position, UpdateSourceTrigger=PropertyChanged}"
                Margin="0,5"/>

            <TextBox
                materialDesign:HintAssist.Hint="رقم الهاتف"
                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                Text="{Binding PhoneNumber, UpdateSourceTrigger=PropertyChanged}"
                Margin="0,5"/>

            <TextBox
                materialDesign:HintAssist.Hint="البريد الإلكتروني"
                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                Margin="0,5"/>

            <ComboBox
                materialDesign:HintAssist.Hint="القسم"
                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                ItemsSource="{Binding Departments}"
                DisplayMemberPath="Name"
                SelectedItem="{Binding Department}"
                Margin="0,5"/>
        </StackPanel>

        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Left"
                    Margin="0,20,0,0">
            <Button Content="حفظ"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                    IsEnabled="{Binding IsValid}"
                    Margin="0,0,10,0"/>
            <Button Content="إلغاء"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                    CommandParameter="False"/>
        </StackPanel>
    </Grid>
</mah:MetroWindow>
