<UserControl x:Class="KMPS.Views.EmployeesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d">
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <TextBlock Text="الموظفون"
                   Grid.Row="0"
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   Margin="0 0 0 16"/>

        <StackPanel Grid.Row="1" 
                    Orientation="Horizontal" 
                    Margin="0 0 0 16">
            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                    materialDesign:ButtonAssist.CornerRadius="4"
                    Command="{Binding AddEmployeeCommand}">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Plus" 
                                           Width="24" 
                                           Height="24"/>
                    <TextBlock Text="موظف جديد" 
                             Margin="8 0 0 0"/>
                </StackPanel>
            </Button>
        </StackPanel>

        <materialDesign:Card Grid.Row="2">
            <DataGrid x:Name="EmployeesDataGrid"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      ItemsSource="{Binding Employees}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الموظف" 
                                      Binding="{Binding EmployeeId}"/>
                    <DataGridTextColumn Header="الاسم الكامل" 
                                      Binding="{Binding FullName}"/>
                    <DataGridTextColumn Header="القسم" 
                                      Binding="{Binding Department.DepartmentName}"/>
                    <DataGridTextColumn Header="المسمى الوظيفي" 
                                      Binding="{Binding Position}"/>
                    <DataGridTextColumn Header="البريد الإلكتروني" 
                                      Binding="{Binding Email}"/>
                    <DataGridTextColumn Header="رقم الهاتف" 
                                      Binding="{Binding PhoneNumber}"/>
                    <DataGridTemplateColumn Header="الإجراءات">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            Command="{Binding DataContext.EditEmployeeCommand, RelativeSource={RelativeSource AncestorType=UserControl}}">
                                        <materialDesign:PackIcon Kind="Pencil"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            Command="{Binding DataContext.DeleteEmployeeCommand, RelativeSource={RelativeSource AncestorType=UserControl}}">
                                        <materialDesign:PackIcon Kind="Delete"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
