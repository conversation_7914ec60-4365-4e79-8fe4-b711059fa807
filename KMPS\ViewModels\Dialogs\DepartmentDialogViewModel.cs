using System.Threading.Tasks;
using KMPS.Models;
using KMPS.Services;

namespace KMPS.ViewModels.Dialogs
{
    public class DepartmentDialogViewModel : DialogViewModelBase
    {
        private string _departmentName = string.Empty;
        private string _description = string.Empty;
        private Department? _department;

        public string DepartmentName
        {
            get => _departmentName;
            set
            {
                if (SetProperty(ref _departmentName, value))
                {
                    ValidateInput();
                }
            }
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public DepartmentDialogViewModel(IDataService dataService, IDialogService dialogService, Department? department = null)
            : base(dataService, dialogService, department == null ? "قسم جديد" : "تعديل القسم")
        {
            _department = department;
            if (department != null)
            {
                DepartmentName = department.Name;
                Description = department.Description ?? string.Empty;
            }
        }

        protected override void ValidateInput()
        {
            IsValid = !string.IsNullOrWhiteSpace(DepartmentName);
            base.ValidateInput();
        }

        public override async Task<bool> SaveAsync()
        {
            if (!IsValid) return false;

            try
            {
                if (_department == null)
                {
                    _department = new Department
                    {
                        Name = DepartmentName,
                        Description = Description
                    };
                    await DataService.AddDepartmentAsync(_department);
                }
                else
                {
                    _department.Name = DepartmentName;
                    _department.Description = Description;
                    await DataService.UpdateDepartmentAsync(_department);
                }

                return true;
            }
            catch
            {
                await DialogService.ShowErrorAsync("خطأ", "حدث خطأ أثناء حفظ القسم");
                return false;
            }
        }
    }
}
