using System.Collections.Generic;

namespace KMPS.Models
{
    public class Task
    {
        public int Id { get; set; }
        public required string Title { get; set; }
        public string? Description { get; set; }
        public int ProjectId { get; set; }
        public int AssignedToId { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? StartDate { get; set; }
        public DateTime DueDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        public required string Status { get; set; } = "جديدة"; // New
        public required string Priority { get; set; } = "متوسطة"; // Medium
        public int Progress { get; set; }
        public string? Notes { get; set; }
        public DateTime? ModifiedAt { get; set; }

        public virtual Project Project { get; set; } = null!;
        public virtual Employee AssignedTo { get; set; } = null!;
        public virtual ICollection<Document> Documents { get; set; } = [];
    }
}
