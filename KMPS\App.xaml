<Application x:Class="KMPS.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:KMPS"             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="http://metro.mahapps.com/winfx/xaml/controls"
             >
    <Application.Resources>
        <ResourceDictionary>            <ResourceDictionary.MergedDictionaries>
                <!-- MahApps -->
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Fonts.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Themes/Light.Blue.xaml" />
                
                <!-- Material Design -->
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Light.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.BlueGrey.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Accent/MaterialDesignColor.Lime.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Material Design Settings -->
            <materialDesign:BundledTheme x:Key="MaterialDesignTheme" BaseTheme="Light" PrimaryColor="BlueGrey" SecondaryColor="Lime" />
            
            <!-- Global Styles -->
            <Style TargetType="{x:Type Window}" BasedOn="{StaticResource {x:Type Window}}">
                <Setter Property="FontFamily" Value="Arial" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
            </Style>
            
            <Style TargetType="{x:Type TextBlock}" BasedOn="{StaticResource MaterialDesignTextBlock}">
                <Setter Property="FontFamily" Value="Arial" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
            </Style>
            
            <Style TargetType="{x:Type TextBox}" BasedOn="{StaticResource MaterialDesignTextBox}">
                <Setter Property="FontFamily" Value="Arial" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
