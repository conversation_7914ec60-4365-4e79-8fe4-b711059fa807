using System.Collections.Generic;

namespace KMPS.Models
{
    public class Project
    {
        public int Id { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal Budget { get; set; }
        public decimal? ActualCost { get; set; }
        public required string Status { get; set; } = "جديد"; // New
        public string? Location { get; set; }
        public string? ContractorName { get; set; }
        public string? ContractorPhone { get; set; }
        public string? ContractorEmail { get; set; }
        public int? ProjectManagerId { get; set; }
        public string? Priority { get; set; } = "متوسطة"; // Medium
        public int? Progress { get; set; } = 0;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? ModifiedAt { get; set; }

        public virtual Employee? ProjectManager { get; set; }
        public virtual ICollection<Task> Tasks { get; set; } = [];
        public virtual ICollection<Document> Documents { get; set; } = [];
    }
}
