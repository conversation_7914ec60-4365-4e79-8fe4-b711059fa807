using System.Threading.Tasks;
using KMPS.Models;

namespace KMPS.Services
{
    public interface IDialogService
    {
        Task<bool> ShowConfirmationAsync(string title, string message);
        System.Threading.Tasks.Task ShowErrorAsync(string title, string message);
        System.Threading.Tasks.Task ShowInfoAsync(string title, string message);
        
        System.Threading.Tasks.Task<Department?> ShowDepartmentDialogAsync(Department? department = null);
        System.Threading.Tasks.Task<Employee?> ShowEmployeeDialogAsync(Employee? employee = null);
        System.Threading.Tasks.Task<Project?> ShowProjectDialogAsync(Project? project = null);
        System.Threading.Tasks.Task<Models.Task?> ShowTaskDialogAsync(Models.Task? task = null);
    }
}
