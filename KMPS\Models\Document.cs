

namespace KMPS.Models
{
    public class Document
    {
        public int Id { get; set; }
        public required string Name { get; set; }
        public required string FilePath { get; set; }
        public required string FileType { get; set; }
        public long FileSize { get; set; }
        public string? Description { get; set; }
        public int? ProjectId { get; set; }
        public int? TaskId { get; set; }
        public int UploadedById { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? ModifiedAt { get; set; }

        public virtual Project? Project { get; set; }
        public virtual Task? Task { get; set; }
        public virtual Employee UploadedBy { get; set; } = null!;
    }
}
